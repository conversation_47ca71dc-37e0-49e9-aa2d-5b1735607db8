# test_speech_filter_stt.py
import argparse
import logging

import librosa
import numpy as np
from scipy.io import wavfile

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def compute_spectral_flatness(data):
    try:
        mag_sq = np.abs(np.fft.rfft(data)) ** 2
        if np.all(mag_sq == 0):
            logger.debug("Zero magnitude in FFT, returning default flatness")
            return 1.0
        geo_mean = np.exp(np.mean(np.log(mag_sq + 1e-10)))
        arith_mean = np.mean(mag_sq)
        return geo_mean / arith_mean
    except Exception as e:
        logger.error(f"Error in spectral flatness: {e}")
        return 1.0


def compute_spectral_centroid(data, sr):
    try:
        fft = np.fft.rfft(data)
        mag = np.abs(fft)
        freq = np.linspace(0, sr / 2, len(mag))
        return np.sum(freq * mag) / np.sum(mag) if np.sum(mag) > 0 else 0
    except Exception as e:
        logger.error(f"Error in spectral centroid: {e}")
        return 0


def detect_speech(segment, sr):
    try:
        energy = np.sum(segment ** 2) / len(segment)
        if energy < 0.001:
            logger.debug(f"Segment energy too low: {energy}")
            return False

        # Spectral flatness
        mag_sq = np.abs(np.fft.rfft(segment)) ** 2
        if np.all(mag_sq == 0):
            logger.debug("Zero magnitude in FFT")
            return False
        geo_mean = np.exp(np.mean(np.log(mag_sq + 1e-10)))
        arith_mean = np.mean(mag_sq)
        flatness = geo_mean / arith_mean

        # Spectral centroid
        fft = np.fft.rfft(segment)
        mag = np.abs(fft)
        freq = np.linspace(0, sr / 2, len(mag))
        centroid = np.sum(freq * mag) / np.sum(mag) if np.sum(mag) > 0 else 0

        # Spectral rolloff (85% energy point)
        cumsum = np.cumsum(mag_sq)
        rolloff_idx = np.where(cumsum >= 0.85 * cumsum[-1])[0][0]
        rolloff = freq[rolloff_idx] if rolloff_idx < len(freq) else freq[-1]

        # Zero-crossing rate
        zcr = np.mean(np.abs(np.diff(np.sign(segment))))

        # Spectral entropy
        mag_norm = mag_sq / np.sum(mag_sq) if np.sum(mag_sq) > 0 else mag_sq
        entropy = -np.sum(mag_norm * np.log2(mag_norm + 1e-10)) if np.any(mag_norm > 0) else 0

        logger.debug(
            f"Flatness: {flatness:.6f}, Centroid: {centroid:.1f}, Rolloff: {rolloff:.1f}, ZCR: {zcr:.3f}, Entropy: {entropy:.3f}")

        # Updated heuristics
        return (
                flatness > 1e-4 and
                flatness < 0.03 and
                centroid > 500 and
                centroid < 3500 and
                rolloff < 4000 and
                zcr > 0.07 and
                entropy > 5.0
        )
    except Exception as e:
        logger.error(f"Error in is_speech: {e}")
        return False


def resample_audio(audio, input_sr, target_sr=16000):
    """Resample audio to target sample rate using librosa."""
    try:
        logger.info(f"Resampling from {input_sr} Hz to {target_sr} Hz")
        resampled = librosa.resample(audio, orig_sr=input_sr, target_sr=target_sr)
        return resampled
    except Exception as e:
        logger.error(f"Error resampling audio: {e}")
        raise


def analyze_wav_file(wav_path, window_size=2.0, target_sr=16000):
    logger.info(f"Analyzing WAV file: {wav_path}")
    try:
        sr, audio = wavfile.read(wav_path)
        logger.info(f"Loaded WAV file with sample rate: {sr} Hz, samples: {len(audio)}")
    except Exception as e:
        logger.error(f"Error reading WAV file: {e}")
        return

    if len(audio.shape) > 1:
        logger.info("Converting stereo to mono")
        audio = audio[:, 0]
    audio = audio.astype(np.float32) / 32768.0

    if sr != target_sr:
        try:
            audio = resample_audio(audio, sr, target_sr)
            sr = target_sr
        except Exception as e:
            logger.error(f"Resampling failed: {e}")
            return

    window_samples = int(window_size * sr)
    segments = [audio[i:i + window_samples] for i in range(0, len(audio), window_samples)]
    logger.info(f"Processing {len(segments)} segments of {window_size}s each")

    timeline = []
    current_start = 0
    current_is_speech = None

    for i, segment in enumerate(segments):
        if len(segment) < window_samples // 2:
            logger.debug(f"Skipping short segment at {i * window_size}s")
            break
        is_speech_segment = detect_speech(segment, sr)
        end_time = (i + 1) * window_size

        if current_is_speech is None:
            current_is_speech = is_speech_segment
            current_start = i * window_size
        elif current_is_speech != is_speech_segment:
            timeline.append((current_start, end_time, current_is_speech))
            current_is_speech = is_speech_segment
            current_start = i * window_size

    if current_is_speech is not None:
        timeline.append((current_start, len(audio) / sr, current_is_speech))

    if not timeline:
        logger.warning("No segments detected in the audio file")
        print("No segments detected")
        return

    for start, end, is_speech in timeline:
        status = "speech" if is_speech else "not speech"
        print(f"{start:.1f}s-{end:.1f}s {status}")
        logger.info(f"{start:.1f}s-{end:.1f}s {status}")


def main():
    parser = argparse.ArgumentParser(description="Analyze WAV file for speech segments")
    parser.add_argument("wav_file", help="Path to the WAV file")
    args = parser.parse_args()

    analyze_wav_file(args.wav_file)


if __name__ == "__main__":
    main()
