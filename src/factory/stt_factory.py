import os

from livekit.plugins import deepgram, openai

from agents.language_settings import LanguageSettings
from app.config import get_config

config = get_config()
lang_settings = LanguageSettings()


def create_stt(language: str = "eng", streaming: bool = False):
    locale = lang_settings.get_locale(language)

    if config.app.stt_provider.lower() == "openai" or language == 'ukr' or language == 'ara':
        return openai.STT(language=language, detect_language=True, model='gpt-4o-mini-transcribe', use_realtime=True)
    else:  # default to deepgram
        lang_name, _ = lang_settings.map_language_code(language)
        return deepgram.STT(
            language=locale,
            api_key=config.deepgram.api_key,
            model=config.deepgram.model,
        )
