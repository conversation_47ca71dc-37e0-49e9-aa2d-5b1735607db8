# agent.py (relevant excerpt)
import asyncio
import logging
from contextlib import suppress
from typing import Any, AsyncIterable

import sentry_sdk
from livekit import rtc
from livekit.agents import Agent, ModelSettings, function_tool, RunContext, AgentSession, stt
from livekit.agents.llm import llm
from livekit.api import LiveKitAP<PERSON>
from livekit.plugins import deepgram
from livekit.protocol.room import UpdateParticipantRequest, DeleteRoomRequest, RoomParticipantIdentity

from agents.settings import AgentSettings
from app.base_svcs import get_langfuse
from app.config import get_config
from conv.analysers.analyser import CallAnalyser
from conv.features.speech_filter_stt import SpeechFilterSTT
from conv.features.voicemail_detector import create_voicemail_detector
from log.sentry_decorators import sentry_span
from log.advagency_logger import get_agent_logger

_config = get_config()
_logger = logging.getLogger(__name__)
_logger.propagate = False
_langfuse = get_langfuse()


class JanaAgent(Agent):
    def __init__(
            self,
            opts: AgentSettings,
            conv_id: str,
            company_id: str,
            timezone: str,
            analyser: CallAnalyser,
            api_client: Any,
            room: rtc.Room,
    ) -> None:
        self.room = room
        self.agent_settings = opts
        self.langfuse = get_langfuse()
        self.conv_id = conv_id
        self.company_id = company_id
        self.timezone = timezone
        self.analyser = analyser
        self.api_client = api_client
        self.trace = None
        self._ending = None
        self.base_stt = deepgram.STT()
        self.speech_stt = SpeechFilterSTT(self.base_stt)
        
        # Initialize voicemail detector
        self.voicemail_detector = create_voicemail_detector(
            confidence_threshold=0.7,
            on_voicemail_detected=self._handle_voicemail_detected
        )

        super().__init__(
            instructions=opts.get_system_prompt(),
            # turn_detection="stt"
        )
        self._initial_tools = [
            self.schedule_callback,
            self.donot_call,
            self.end_conversation,
            self.voicemail_detector.get_voicemail_function_tool()
        ]

        if "send_follow_up_message" in opts.config.functions:
            self._initial_tools.append(self.send_follow_up_message)
        _logger.debug(f"Prepared tools: {[t.__name__ for t in self._initial_tools]}")

    async def initialize(self):
        _logger.info(f"Initializing agent with tools: {[t.__name__ for t in self._initial_tools]}")
        await self.update_tools(self._initial_tools)

    async def stt_node(self, audio: AsyncIterable[rtc.AudioFrame], model_settings: ModelSettings) -> AsyncIterable[
        stt.SpeechEvent]:
        """
        Stream audio into our SpeechFilterSTT while iterating its events.
        Important: feed frames concurrently; do NOT push-then-read.
        """
        try:
            self.base_stt = deepgram.STT()  # keep your existing base
            self.speech_stt = SpeechFilterSTT(self.base_stt)  # uses gate
            stream = self.speech_stt.stream(sample_rate=16000)

            async def feeder():
                async for frame in audio:
                    stream.push_frame(frame)

            feed_task = asyncio.create_task(feeder())

            try:
                async for event in stream:
                    # from here on, events only start after gate decides it's speech
                    yield event
            finally:
                feed_task.cancel()
                with suppress(Exception):
                    await feed_task
                stream.close()
        except Exception as e:
            _logger.exception("stt_node failure: %s", e)
            raise
    async def llm_node(
            self,
            chat_ctx: llm.ChatContext,
            tools: list[llm.FunctionTool],
            model_settings: ModelSettings,
    ) -> AsyncIterable[llm.ChatChunk]:
        return Agent.default.llm_node(self, chat_ctx, tools, model_settings)

    @function_tool
    @sentry_span(op="func_call", description="followup conversation")
    async def send_follow_up_message(self, context: RunContext):
        """When the user requests a follow-up or details, so agent will send a message to current phone number using WhatsApp. Agent will send a followup to whatsapp and quickly confirm that"""
        await self.api_client.request_follow_up(conversation_id=self.conv_id)
        _logger.info(f"exec: send_follow_up_message")
        return "follow up requested. agent will disconnect after saying goodbye"

    @function_tool
    @sentry_span(op="func_call", description="schedule_callback function call")
    async def schedule_callback(self, context: RunContext, date_time: str) -> str:
        """When the user requests a callback at a some date and time, schedule the callback in the system and responds with just a few words of confirmation.
          Args:
              "date_time": Specifies the date and time to recall to this client in ISO format. Use your knowledge about current date and time. If a specific time is not mentioned, assume 10:00 AM. If no date is specified, assume the next business day. Ensure the datetime is in the future and within business hours"""
        _logger.warning(f"exec: schedule_callback {date_time}")
        await self.api_client.request_callback(conversation_id=self.conv_id, callback_time=date_time)
        return "callback where scheduled. agent should say goodbye"

    @function_tool
    @sentry_span(op="func_call", description="donotcall function call")
    async def donot_call(self, context: RunContext):
        """Set the conversation status to ‘Do Not Call’ when the user explicitly states that they do not wish to receive further communication, such as by saying ‘do not call again,’ ‘please remove me from your call list,’ or any equivalent phrase indicating they do not want to be contacted again"""
        _logger.info(f"exec: donot_call")
        await self.api_client.set_do_not_call(conversation_id=self.conv_id)
        await self.finish_call(context.session)
        return "callee do not want to talk again. agent should say goodbye and quickly finish"

    async def finish_call(self, session: AgentSession, delay_seconds: int = 5):
        """Gracefully finish the call and schedule room deletion"""
        try:
            _logger.info(f"Starting graceful call finish for room: {self.room.name}")

            await self._terminate_sip_participants()

            await self.analyser.build_conversation_result(
                conversation_id=self.conv_id,
                conversation_history=session.history,
                room_id=self.room.name,
                session=session
            )

            await self.update_agent_attributes({"agent.callStatus": "endCall"})

            _logger.info("Closing agent session...")
            await session.aclose()

            _logger.info("Disconnecting from room...")
            await self.room.disconnect()

            _logger.info(f"Scheduling room deletion in {delay_seconds} seconds...")
            await self._schedule_room_deletion(delay_seconds)

        except Exception as e:
            _logger.error(f"Error during graceful call finish: {e}", exc_info=True)
            sentry_sdk.capture_exception(e)
            raise

    async def _terminate_sip_participants(self):
        """Terminate SIP participants by removing them from the room"""
        try:
            sip_participants = []
            for identity, participant in self.room.remote_participants.items():
                if (identity.startswith('caller-') or
                        any(char.isdigit() for char in identity) and len(identity) > 8):
                    sip_participants.append((identity, participant))
                    _logger.info(f"Found SIP participant: {identity} (SID: {participant.sid})")

            if not sip_participants:
                _logger.info("No SIP participants found in room")
                return

            livekit_api = LiveKitAPI()

            try:
                for identity, participant in sip_participants:
                    try:
                        _logger.info(f"Terminating SIP call for participant: {identity}")
                        await livekit_api.room.remove_participant(
                            RoomParticipantIdentity(
                                room=self.room.name,
                                identity=identity
                            )
                        )
                        _logger.info(f"✅ SIP call terminated for {identity}")
                    except Exception as e:
                        _logger.error(f"Failed to terminate SIP call for {identity}: {e}")
                        sentry_sdk.capture_exception(e)
            finally:
                await livekit_api.aclose()

            _logger.info(f"SIP call termination completed for {len(sip_participants)} participants")

        except Exception as e:
            _logger.error(f"Error during SIP participant termination: {e}", exc_info=True)
            sentry_sdk.capture_exception(e)

    async def _schedule_room_deletion(self, delay_seconds: int):
        """Schedule room deletion after a delay"""
        import asyncio

        try:
            await asyncio.sleep(delay_seconds)
            livekit_api = LiveKitAPI()
            try:
                _logger.info(f"Deleting room: {self.room.name}")
                await livekit_api.room.delete_room(DeleteRoomRequest(
                    room=self.room.name
                ))
                _logger.info(f"✅ Room {self.room.name} deleted successfully")
            except Exception as e:
                _logger.error(f"Failed to delete room {self.room.name}: {e}")
                sentry_sdk.capture_exception(e)
            finally:
                await livekit_api.aclose()

        except Exception as e:
            _logger.error(f"Error in scheduled room deletion: {e}", exc_info=True)
            sentry_sdk.capture_exception(e)

    @function_tool
    @sentry_span(op="func_call", description="end conversation function call")
    async def end_conversation(self, context: RunContext):
        """End conversation, when user do not want to continue or want to terminate the session."""
        await context.session.generate_reply(
            instructions="say goodbye to the user using common friendly but formal phrases")
        await self.finish_call(context.session)

    @sentry_span(op="agent.voicemail", description="Handle voicemail detection")
    async def _handle_voicemail_detected(self, transcribed_text: str, confidence: float):
        """Handle when voicemail is detected by the voicemail detector"""
        _logger.info(f"🎯 Voicemail detected with {confidence:.1%} confidence!")
        _logger.info(f"Voicemail text: {transcribed_text[:200]}...")
        
        try:
            # Update agent attributes to indicate voicemail detected
            await self.update_agent_attributes({
                "agent.voicemail_detected": "true",
                "agent.voicemail_confidence": str(confidence)
            })
            await self.finish_call(self.session)
            # Set conversation status for analytics
           # await self.api_client.set_voicemail_detected(conversation_id=self.conv_id)
            
            # Log the voicemail detection for analysis
            if self.langfuse:
                try:
                    self.langfuse.score(
                        name="voicemail_detection",
                        value=confidence,
                        comment=f"Voicemail detected: {transcribed_text[:100]}..."
                    )
                except Exception as e:
                    _logger.warning(f"Failed to log voicemail to Langfuse: {e}")
            
            _logger.info("Voicemail detection handling completed successfully")
            
        except Exception as e:
            _logger.error(f"Error handling voicemail detection: {e}", exc_info=True)
            sentry_sdk.capture_exception(e)

    async def update_agent_attributes(self, attributes: dict) -> None:
        try:
            lkapi = LiveKitAPI()
            await lkapi.room.update_participant(
                UpdateParticipantRequest(
                    room=self.room.name,
                    identity=self.room.local_participant.identity,
                    attributes=attributes,
                ),
            )
            await lkapi.aclose()
        except Exception as e:
            _logger.error(f"Failed to update participant attributes: {e}")
            sentry_sdk.capture_exception(e)

    async def tts_node(
            self, text: AsyncIterable[str], model_settings: ModelSettings
    ) -> AsyncIterable[llm.ChatChunk]:
        return Agent.default.tts_node(self, text, model_settings)

    @sentry_span(op="agent.say_idle_message", description="Agent Say Idle Message")
    def say_idle_message(self):
        try:
            if hasattr(self, 'session') and self.session is not None and self.session._activity is not None:
                self.session.generate_reply(instructions="confirm if user is still there")
        except Exception as e:
            logging.getLogger(__name__).warning(f"Failed to say idle message: {e}")