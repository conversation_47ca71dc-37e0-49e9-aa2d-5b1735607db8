import asyncio
import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime

from opentelemetry.metrics import Counter, Histogram, UpDownCounter
from telemetry_config import get_meter, is_telemetry_enabled

_logger = logging.getLogger(__name__)

@dataclass
class ConversationMetrics:
    """Track metrics for a conversation session"""
    start_time: float = field(default_factory=time.time)
    message_count: int = 0
    error_count: int = 0
    handoff_count: int = 0
    participant_count: int = 0
    last_activity: float = field(default_factory=time.time)


class AgentMetricsCollector:
    """Collects and reports minimal metrics for LiveKit Agents"""
    def __init__(self, service_name: str = "advagency-conversation-manager"):
        self.service_name = service_name
        self._conversations: Dict[str, ConversationMetrics] = {}
        self._meter = None
        self._metrics_initialized = False
        # Minimal metrics
        self.agent_requests_total: Optional[Counter] = None
        self.agent_request_duration: Optional[Histogram] = None
        self.agent_errors_total: Optional[Counter] = None
        self.agent_active_sessions: Optional[UpDownCounter] = None
        self.agent_active_conversations: Optional[UpDownCounter] = None
        self.conversation_duration: Optional[Histogram] = None
        if is_telemetry_enabled():
            self._initialize_metrics()

    def _initialize_metrics(self):
        """Initialize minimal OpenTelemetry metrics"""
        try:
            self._meter = get_meter(self.service_name)
            self.agent_requests_total = self._meter.create_counter(
                name="agent_requests_total",
                description="Total number of agent requests",
                unit="1",
            )
            self.agent_request_duration = self._meter.create_histogram(
                name="agent_request_duration_seconds",
                description="Agent request duration in seconds",
                unit="s",
            )
            self.agent_errors_total = self._meter.create_counter(
                name="agent_errors_total",
                description="Total number of agent errors",
                unit="1",
            )
            self.agent_active_sessions = self._meter.create_up_down_counter(
                name="agent_active_sessions",
                description="Number of active agent sessions",
                unit="1",
            )
            self.agent_active_conversations = self._meter.create_up_down_counter(
                name="agent_active_conversations",
                description="Number of active conversations",
                unit="1",
            )
            self.conversation_duration = self._meter.create_histogram(
                name="conversation_duration_seconds",
                description="Conversation duration in seconds",
                unit="s",
            )
            self._metrics_initialized = True
            _logger.info("Metrics collector initialized successfully")
        except Exception as e:
            _logger.error(f"Failed to initialize metrics: {e}", exc_info=True)

    def start_conversation(self, conversation_id: str, participant_count: int = 1) -> None:
        """Start tracking a new conversation"""
        if not self._metrics_initialized:
            return
        self._conversations[conversation_id] = ConversationMetrics(
            participant_count=participant_count
        )
        if self.agent_active_conversations:
            self.agent_active_conversations.add(1)
        _logger.debug(f"Started tracking conversation: {conversation_id}")

    def end_conversation(self, conversation_id: str) -> None:
        """End tracking a conversation and record final metrics"""
        if not self._metrics_initialized or conversation_id not in self._conversations:
            return
        conv_metrics = self._conversations.pop(conversation_id)
        duration = time.time() - conv_metrics.start_time
        if self.agent_active_conversations:
            self.agent_active_conversations.add(-1)
        if self.conversation_duration:
            self.conversation_duration.record(duration)
        _logger.debug(f"Ended tracking conversation: {conversation_id}, duration: {duration:.2f}s")

    @asynccontextmanager
    async def track_request(self, request_type: str = "general"):
        """Context manager to track request duration"""
        start_time = time.time()
        if self._metrics_initialized and self.agent_requests_total:
            self.agent_requests_total.add(1, {"request_type": request_type})
        try:
            yield
        except Exception as e:
            if self._metrics_initialized and self.agent_errors_total:
                self.agent_errors_total.add(1, {"request_type": request_type, "error_type": type(e).__name__})
            raise
        finally:
            duration = time.time() - start_time
            if self._metrics_initialized and self.agent_request_duration:
                self.agent_request_duration.record(duration, {"request_type": request_type})

    def record_session_start(self) -> None:
        """Record a new agent session start"""
        if self._metrics_initialized and self.agent_active_sessions:
            self.agent_active_sessions.add(1)

    def record_session_end(self) -> None:
        """Record an agent session end"""
        if self._metrics_initialized and self.agent_active_sessions:
            self.agent_active_sessions.add(-1)

    async def shutdown(self):
        """Shutdown the metrics collector"""
        _logger.info("Metrics collector shutdown complete")


# Global metrics collector instance
_metrics_collector: Optional[AgentMetricsCollector] = None

def get_metrics_collector() -> AgentMetricsCollector:
    """Get the global metrics collector instance"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = AgentMetricsCollector()
    return _metrics_collector

def init_metrics_collector(service_name: str = "advagency-conversation-manager") -> AgentMetricsCollector:
    """Initialize the global metrics collector"""
    global _metrics_collector
    _metrics_collector = AgentMetricsCollector(service_name)
    return _metrics_collector