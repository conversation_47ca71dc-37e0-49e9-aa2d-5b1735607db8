#!/usr/bin/env bash
set -euo pipefail

# -------- defaults (edit if needed) ----------
LK_URL="${LK_URL:-ws://localhost:7880}"                 # your LiveKit server (ws://localhost:7880)
VIEWER_URL_BASE="${VIEWER_URL_BASE:-http://localhost:3000/custom}"
LK_CLI="${LK_CLI:-lk}"

COMPANY_ID="${COMPANY_ID:-3dd09fe0-f892-4354-a4f2-9c94667186d1}"
CAMPAIGN_ID="${CAMPAIGN_ID:-acfeed64-7d08-485b-a72e-ce1155582a19}"

# -------- helpers ----------
have_cmd() { command -v "$1" >/dev/null 2>&1; }

gen_uuid() {
  if have_cmd uuidgen; then uuidgen | tr '[:upper:]' '[:lower:]'
  elif have_cmd powershell.exe; then powershell.exe -NoProfile -Command "[guid]::NewGuid().ToString().ToLower()"
  else date +%s | sha1sum | awk '{print $1}'; fi
}

iso_now_utc() { date -u +"%Y-%m-%dT%H:%M:%SZ"; }

open_url() {
  local url="$1"
  if [[ "$OSTYPE" == "darwin"* ]] && have_cmd open; then open "$url"; return; fi
  if [[ "$OSTYPE" == "msys"* || "$OSTYPE" == "cygwin"* ]]; then
    if have_cmd powershell.exe; then powershell.exe -NoProfile -Command "Start-Process \"$url\"" >/dev/null 2>&1 || true; return; fi
    if have_cmd cmd.exe; then cmd.exe /c start "" "$url" >/dev/null 2>&1 || true; return; fi
  fi
  if have_cmd wslview; then wslview "$url"
  elif have_cmd xdg-open; then xdg-open "$url" >/dev/null 2>&1 || true
  else echo "Open manually: $url"; fi
}

# Try both new & old CLI verbs for dispatch
dispatch_create() {
  local agent_name="$1" room_name="$2" meta="$3"
  if $LK_CLI --url "$LK_URL" agent dispatch --help >/dev/null 2>&1; then
    $LK_CLI --url "$LK_URL" agent dispatch create --agent-name "$agent_name" --room "$room_name" --metadata "$meta" >/dev/null
  elif $LK_CLI --url "$LK_URL" dispatch --help >/dev/null 2>&1; then
    $LK_CLI --url "$LK_URL" dispatch create --agent-name "$agent_name" --room "$room_name" --metadata "$meta" >/dev/null
  else
    echo "ERROR: Your lk CLI does not support agent dispatch commands." >&2
    exit 1
  fi
}

# -------- args ----------
AGENT_ID="emma"
PHONE="+************"
NAME="Daria"
EMAIL=""
COUNTRY=""

while [[ $# -gt 0 ]]; do
  case "$1" in
    --agent-id) AGENT_ID="$2"; shift 2 ;;
    --phone)    PHONE="$2"; shift 2 ;;
    --name)     NAME="$2"; shift 2 ;;
    --email)    EMAIL="$2"; shift 2 ;;
    --country)  COUNTRY="$2"; shift 2 ;;
    *) echo "Unknown arg: $1"; exit 1 ;;
  esac
done

CONV_ID="$(gen_uuid)"
ROOM_NAME="inbound-call:company-id:${COMPANY_ID}:campaign-id:${CAMPAIGN_ID}:conversation-id:${CONV_ID}"

TIMESTAMP="$(iso_now_utc)"
ROOM_META_COMPACT='{ "agent": { "id": "'"$AGENT_ID"'" }, "type": "inbound-call", "context": { "companyId": "'"$COMPANY_ID"'", "campaignId": "'"$CAMPAIGN_ID"'", "conversationId": "'"$CONV_ID"'", "participant": { "phone": "'"$PHONE"'", "name": "'"$NAME"'", "email": "'"$EMAIL"'", "country": "'"$COUNTRY"'", "timestamp": "'"$TIMESTAMP"'" } } }'

echo "Room: $ROOM_NAME"
echo "Metadata: $ROOM_META_COMPACT"

# 1) Create room (no --metadata because your CLI doesn't support it)
echo "Creating room..."
if ! $LK_CLI --url "$LK_URL" room create "$ROOM_NAME" --empty-timeout 3600 >/dev/null 2>&1; then
  echo "Note: room may already exist or CLI returned a warning; continuing..."
fi

# 2) Update metadata with retries (ensure room exists first)
echo "Updating room metadata..."
tries=0
until $LK_CLI --url "$LK_URL" room update "$ROOM_NAME" --metadata "$ROOM_META_COMPACT" >/dev/null 2>&1; do
  tries=$((tries+1))
  if (( tries > 10 )); then
    echo "ERROR: failed to update room metadata after 10 attempts"; exit 1
  fi
  sleep 0.5
done

# 3) Dispatch agent AFTER metadata is set
echo "Dispatching agent: $AGENT_ID"
if ! dispatch_create "$AGENT_ID" "$ROOM_NAME" "$ROOM_META_COMPACT"; then
  echo "ERROR: dispatch create failed"; exit 1
fi

# 4) Create callee token & open viewer
echo "Creating access token via lk CLI..."
TOKEN_OUT="$($LK_CLI --url "$LK_URL" token create --join --room "$ROOM_NAME" --identity callee)"
ACCESS_TOKEN="$(printf '%s\n' "$TOKEN_OUT" | sed -n 's/.*Access token: \(.*\)/\1/p')"
if [[ -z "$ACCESS_TOKEN" ]]; then echo "Failed to extract access token"; exit 1; fi
echo "Access token: $ACCESS_TOKEN"

# clipboard on Windows
if have_cmd clip.exe; then printf '%s' "$ACCESS_TOKEN" | clip.exe; echo "Access token copied to clipboard (clip.exe)."; fi

VIEW_URL="${VIEWER_URL_BASE}?liveKitUrl=${LK_URL}&token=${ACCESS_TOKEN}"
echo "Opening viewer: $VIEW_URL"
open_url "$VIEW_URL"

echo "Done."
