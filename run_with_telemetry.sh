#!/bin/bash

echo "🔭 Running with TELEMETRY/SigNoz integration"
echo "   - Logs and traces sent to SigNoz"
echo "   - Environment: production (or 'local' if using defaults)"
echo "   - Full observability enabled"
echo ""

export LOG_LEVEL=INFO

# Disable LiveKit's duplicate logging
# Force LiveKit to use only one logging format
export LIVEKIT_LOG_LEVEL=info

# SigNoz OpenTelemetry configuration using environment variables
export OTEL_PYTHON_LOGGING_AUTO_INSTRUMENTATION_ENABLED=true
export OTEL_EXPORTER_OTLP_ENDPOINT=https://ingest.us.signoz.cloud:443
export OTEL_EXPORTER_OTLP_HEADERS=signoz-access-token=c80552d9-8531-4668-ac8a-eeb2622fe5f1
export OTEL_SERVICE_NAME=advagency-conversation-manager
export OTEL_SERVICE_VERSION=1.0.0
export OTEL_RESOURCE_ATTRIBUTES=deployment.environment=production

# Configure SDK
export OTEL_METRICS_EXPORTER=otlp
export OTEL_TRACES_EXPORTER=otlp
export OTEL_LOGS_EXPORTER=otlp

# Disable SDK if needed (set to "true" to disable)
export OTEL_SDK_DISABLED=false

# Additional environment variables for the Python telemetry config
export SIGNOZ_ENDPOINT=https://ingest.us.signoz.cloud:443
export SIGNOZ_INGESTION_KEY=c80552d9-8531-4668-ac8a-eeb2622fe5f1
export OTEL_DEPLOYMENT_ENVIRONMENT=production

# Set the port for the LiveKit worker (avoiding the conflict on 8081)
export LIVEKIT_HTTP_PORT=8082

# Default command is "start" unless specified otherwise
COMMAND=${1:-start}

echo "Starting application with command: $COMMAND"
echo "Additional args: ${@:2}"
echo ""

# Run the application with PDM (telemetry is configured in the code)
pdm run python src/main.py "$COMMAND" "${@:2}"