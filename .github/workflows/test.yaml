name: Test
on:
  push:
    branches: [ main ]
  pull_request:

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: pnpm/action-setup@v4
    - name: Use Node.js 22
      uses: actions/setup-node@v4
      with:
        node-version: 22
        cache: 'pnpm'

    - name: Install dependencies
      run: pnpm install

    - name: ESLint
      run: pnpm lint
    
    - name: Prettier
      run: pnpm format:check

    - name: Run Tests
      run: pnpm test
