#!/usr/bin/env python3
"""
<PERSON>ript to make dynamic outbound calls via LiveKit to any phone number
Supports both single calls and batch processing from CSV files
"""

import argparse
import asyncio
import csv
import json
import os
import shutil
import subprocess
import sys
import tempfile
from datetime import datetime, timezone
from pathlib import Path

from call_monitor import CallMonitor

# Try to import LiveKit SDK for monitoring
try:
    from livekit import api
    LIVEKIT_SDK_AVAILABLE = True
except ImportError:
    LIVEKIT_SDK_AVAILABLE = False

# Try to load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("🔧 Environment variables loaded from .env file")
except ImportError:
    print("⚠️  python-dotenv not available, using system environment variables")

CHECK_INTERVAL = 3
RESULTS_DIR = "call_results"

# Recording configuration
REMOTE_HOST = "<EMAIL>"
REMOTE_RECORDINGS_DIR = "/opt/freeswitch-recordings"
LOCAL_RECORDINGS_DIR = "./recordings"


async def create_livekit_room(room_name: str):
    """Create a new LiveKit room."""
    livekit_api = api.LiveKitAPI(
        os.getenv("LIVEKIT_URL"),
        os.getenv("LIVEKIT_API_KEY"),
        os.getenv("LIVEKIT_API_SECRET"),
    )
    try:
        room = await livekit_api.room.create_room(api.CreateRoomRequest(name=room_name))
        print(f"✅ Room '{room_name}' created successfully.")
        return room
    finally:
        await livekit_api.aclose()


async def download_call_recording(phone_number, room_name, target_dir=None, timeout_seconds=30):
    """Download call recording for a specific phone number and move it to target directory"""
    try:
        expected_filename = f"{phone_number}.wav"
        print(f"   🎵 Looking for recording: {expected_filename}")

        recordings_path = Path(LOCAL_RECORDINGS_DIR)
        recordings_path.mkdir(exist_ok=True)

        results_path = target_dir if target_dir else Path(RESULTS_DIR)
        results_path.mkdir(parents=True, exist_ok=True)

        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                print(f"   📥 Downloading recordings from remote host (attempt {attempt + 1}/{max_attempts})...")
                result = subprocess.run(
                    ['rsync', '-avz', '--progress',
                     f'{REMOTE_HOST}:{REMOTE_RECORDINGS_DIR}/',
                     f'{LOCAL_RECORDINGS_DIR}/'],
                    capture_output=True, text=True, timeout=30
                )
                if result.returncode != 0:
                    print(f"   ⚠️  rsync failed: {result.stderr}")
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(5)
                    continue

                recording_file = recordings_path / expected_filename
                if recording_file.exists():
                    final_recording_path = results_path / f"recording_{room_name}.wav"
                    shutil.copy2(recording_file, final_recording_path)
                    print(f"   ✅ Recording downloaded and saved: {final_recording_path}")
                    return str(final_recording_path)

                if attempt < max_attempts - 1:
                    print("   ⏳ Recording not ready yet, waiting 5 seconds...")
                    await asyncio.sleep(5)

            except subprocess.TimeoutExpired:
                print("   ⚠️  rsync timeout, retrying...")
                if attempt < max_attempts - 1:
                    await asyncio.sleep(2)
                continue
            except Exception as e:
                print(f"   ⚠️  Error during recording download: {e}")
                if attempt < max_attempts - 1:
                    await asyncio.sleep(5)
                continue

        print(f"   ❌ Recording not found after {max_attempts} attempts")
        return None

    except Exception as e:
        print(f"   ❌ Failed to download recording: {e}")
        return None


async def make_outbound_call(
        phone_number: str,
        room_name: str | None = None,
        participant_name: str | None = None,
        agent_identity: str = "emma",
        metadata: dict | None = None,
):
    """
    Make an outbound call:
      1) Create room
      2) Set room metadata (includes agent_id)
      3) Dispatch agent
      4) Add SIP participant
    """
    if not phone_number.startswith('+'):
        phone_number = "+" + phone_number

    if not room_name:
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H")
        room_name = f"call-{phone_number}-{timestamp}"

    if not participant_name:
        participant_name = f"Caller to {phone_number}"

    # 1) Create room
    await create_livekit_room(room_name)

    # 2) Prepare metadata (include agent_id)
    md = metadata or {}
    call_metadata = {
        "agent": {"id": agent_identity},
        "agent_id": agent_identity,  # convenience mirror
        "context": {
            "participant": {
                "phone": phone_number,
                "name": participant_name,
                "email": md.get("email", ""),
                "country": md.get("country", ""),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }
    }
    print(f"metadata going to room: {json.dumps(call_metadata, ensure_ascii=False)}")

    livekit_api = api.LiveKitAPI(
        os.getenv("LIVEKIT_URL"),
        os.getenv("LIVEKIT_API_KEY"),
        os.getenv("LIVEKIT_API_SECRET"),
    )
    try:
        await livekit_api.room.update_room_metadata(
            api.UpdateRoomMetadataRequest(
                room=room_name,
                metadata=json.dumps(call_metadata, ensure_ascii=False),
            )
        )
        print(f"✅ Room metadata set for '{room_name}'")
    except Exception as e:
        print(f"⚠️ Warning: Failed to set room metadata: {e}")
    finally:
        await livekit_api.aclose()

    # 3) Dispatch agent (name must match your worker's registered agent name)
    print(f"🚀 Dispatching agent '{agent_identity}' to room '{room_name}'...")
    livekit_api = api.LiveKitAPI(
        os.getenv("LIVEKIT_URL"),
        os.getenv("LIVEKIT_API_KEY"),
        os.getenv("LIVEKIT_API_SECRET"),
    )
    try:
        dispatch = await livekit_api.agent_dispatch.create_dispatch(
            api.CreateAgentDispatchRequest(
                agent_name=agent_identity,  # <--- use the flag value
                room=room_name,
                metadata=json.dumps(call_metadata, ensure_ascii=False),
            )
        )
        print(f"✅ Agent dispatch created. Dispatch ID: {dispatch.id}")
        await asyncio.sleep(10)
    finally:
        await livekit_api.aclose()

    # 4) Add SIP participant
    print(f"📞 Adding remote participant {phone_number} to the room...")
    participant_config = {
        "sip_trunk_id": "ST_EzgYeBNxFiPd",
        "sip_call_to": phone_number,
        "room_name": room_name,
        "participant_identity": f"caller-{phone_number.replace('+', '').replace(' ', '')}",
        "participant_name": participant_name,
    }

    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
        json.dump(participant_config, temp_file, indent=2)
        temp_file_path = temp_file.name

    try:
        result = subprocess.run(
            ['lk', 'sip', 'participant', 'create', temp_file_path],
            capture_output=True, text=True, check=True
        )
        output_lines = result.stdout.strip().split('\n')
        call_details = {}
        for line in output_lines:
            if line.startswith('SIPCallID:'):
                call_details['call_id'] = line.split(': ')[1]
            elif line.startswith('ParticipantID:'):
                call_details['participant_id'] = line.split(': ')[1]

        call_details.update({
            'room_name': room_name,
            'phone_number': phone_number,
            'participant_name': participant_name,
            'agent_id': agent_identity,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'config_used': participant_config
        })
        return call_details

    except subprocess.CalledProcessError as e:
        raise RuntimeError(f"Failed to create SIP participant: {e.stderr}")
    finally:
        os.unlink(temp_file_path)


async def monitor_call_status(room_name, participant_id, timeout_seconds=1800):
    """Monitor call status using enhanced CallMonitor class"""
    monitor = CallMonitor(room_name, participant_id)
    result = await monitor.start_monitoring(timeout_seconds)

    if result.get('events'):
        print(f"\n📊 Call Timeline for {room_name}:")
        timeline = monitor._generate_timeline()
        for entry in timeline:
            print(f"   {entry['time']}: {entry['description']}")

    metrics = result.get('metrics', {})
    if metrics:
        print(f"\n📈 Call Metrics:")
        if metrics.get('time_to_first_join'):
            print(f"   ⏱️  Time to first join: {metrics['time_to_first_join']:.1f}s")
        if metrics.get('actual_call_duration'):
            print(f"   📞 Actual call duration: {metrics['actual_call_duration']:.1f}s")
        if metrics.get('max_concurrent_participants'):
            print(f"   👥 Max participants: {metrics['max_concurrent_participants']}")
        print(f"   🔍 Total monitoring time: {metrics.get('monitoring_duration', 0):.1f}s")

    return result


def export_results_to_csv(results, csv_filename):
    """Export call results to CSV file (flat results directory)"""
    try:
        results_path = Path(RESULTS_DIR)
        results_path.mkdir(exist_ok=True)
        csv_output_path = results_path / csv_filename

        with open(csv_output_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'row', 'name', 'phone', 'call_status', 'call_id',
                'participant_id', 'room_name', 'connected', 'duration',
                'call_duration', 'disconnect_reason', 'sip_status',
                'error_reason', 'timestamp', 'agent_id'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for call in results.get('successful_calls', []):
                call_details = call['call_details']
                status_info = call_details.get('status_info', {})
                business_status = status_info.get('business_call_status', 'UNKNOWN')
                writer.writerow({
                    'row': call['row'],
                    'name': call['name'],
                    'phone': call_details['phone_number'],
                    'call_status': business_status,
                    'call_id': call_details.get('call_id', ''),
                    'participant_id': call_details.get('participant_id', ''),
                    'room_name': call_details.get('room_name', ''),
                    'connected': status_info.get('connected', False),
                    'duration': status_info.get('duration', 0),
                    'call_duration': status_info.get('call_duration', 0),
                    'disconnect_reason': status_info.get('disconnect_reason', ''),
                    'sip_status': status_info.get('sip_status', ''),
                    'error_reason': '',
                    'timestamp': call_details.get('timestamp', ''),
                    'agent_id': call_details.get('agent_id', ''),
                })

            for call in results.get('failed_calls', []):
                writer.writerow({
                    'row': call['row'],
                    'name': call['name'],
                    'phone': call['phone'],
                    'call_status': 'failed',
                    'call_id': '',
                    'participant_id': '',
                    'room_name': '',
                    'connected': False,
                    'duration': 0,
                    'call_duration': 0,
                    'disconnect_reason': 'initiation_failed',
                    'sip_status': '',
                    'error_reason': call['error'],
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'agent_id': '',
                })

        print(f"📊 Results exported to CSV: {csv_output_path}")

    except Exception as e:
        print(f"⚠️  Failed to export CSV: {e}")


def export_results_to_csv_in_batch(results, batch_folder, csv_filename):
    """Export call results to CSV file in specific batch folder"""
    try:
        csv_output_path = batch_folder / csv_filename
        with open(csv_output_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'row', 'name', 'phone', 'call_status', 'room_name',
                'connected', 'monitoring_duration', 'call_duration',
                'disconnect_reason', 'sip_status', 'error_reason',
                'timestamp', 'agent_id',
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for call in results.get('successful_calls', []):
                call_details = call['call_details']
                status_info = call_details.get('status_info', {})
                business_status = status_info.get('business_call_status', 'UNKNOWN')
                writer.writerow({
                    'row': call['row'],
                    'name': call['name'],
                    'phone': call_details['phone_number'],
                    'call_status': business_status,
                    'room_name': call_details.get('room_name', ''),
                    'connected': status_info.get('connected', False),
                    'monitoring_duration': status_info.get('monitoring_duration', 0),
                    'call_duration': status_info.get('call_duration', 0),
                    'disconnect_reason': status_info.get('disconnect_reason', ''),
                    'sip_status': status_info.get('sip_status', ''),
                    'error_reason': '',
                    'timestamp': call_details.get('timestamp', ''),
                    'agent_id': call_details.get('agent_id', ''),
                })

            for call in results.get('failed_calls', []):
                writer.writerow({
                    'row': call['row'],
                    'name': call['name'],
                    'phone': call['phone'],
                    'call_status': 'failed',
                    'room_name': '',
                    'connected': False,
                    'monitoring_duration': 0,
                    'call_duration': 0,
                    'disconnect_reason': 'initiation_failed',
                    'sip_status': '',
                    'error_reason': call['error'],
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'agent_id': '',
                })

        print(f"📊 Results exported to CSV: {csv_output_path}")

    except Exception as e:
        print(f"⚠️  Failed to export CSV: {e}")


async def process_csv_calls_async(csv_filename, max_rows=None, delay_seconds=5, monitor_calls=True, agent_id="emma"):
    """
    Process calls from CSV file one by one with monitoring
    CSV format: Name;Phone;Email;Country;
    """
    successful_calls = []
    failed_calls = []

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_filename = Path(csv_filename).stem
    batch_folder_name = f"batch_{timestamp}_{base_filename}"
    batch_results_path = Path(RESULTS_DIR) / batch_folder_name
    batch_results_path.mkdir(parents=True, exist_ok=True)

    print(f"📁 Created batch folder: {batch_results_path}")

    batch_info = {
        'batch_folder': batch_results_path,
        'batch_name': batch_folder_name,
        'timestamp': timestamp
    }

    try:
        with open(csv_filename, 'r', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile, delimiter=';')
            for row_num, row in enumerate(reader, 1):
                if max_rows and row_num > max_rows:
                    break

                if len(row) < 2:
                    print(f"⚠️  Row {row_num}: Invalid format (not enough columns)")
                    continue

                name = row[0].strip()
                phone = row[1].strip()
                if not phone:
                    print(f"⚠️  Row {row_num}: Empty phone number for {name}")
                    continue
                if not phone.startswith('+'):
                    phone = '+' + phone

                try:
                    print(f"\n🚀 [{row_num}] Making call to {name} at {phone} (agent_id={agent_id})...")

                    metadata = {
                        'name': name,
                        'phone': phone,
                        'csv_row': row_num,
                        'call_timestamp': datetime.now(timezone.utc).isoformat()
                    }
                    if len(row) > 2 and row[2].strip():
                        metadata['email'] = row[2].strip()
                    if len(row) > 3 and row[3].strip():
                        metadata['country'] = row[3].strip()

                    call_details = await make_outbound_call(
                        phone_number=phone,
                        participant_name=name,
                        metadata=metadata,
                        agent_identity=agent_id,
                    )
                    print(f"✅ [{row_num}] Call to {name} initiated! CallID={call_details.get('call_id', '')}")

                    room_name = call_details['room_name']
                    call_file = batch_info['batch_folder'] / f"{room_name}.json"
                    with open(call_file, 'w') as f:
                        json.dump(call_details, f, indent=2)
                    print(f"   📄 Call details saved to: {call_file}")

                    if monitor_calls and call_details.get('participant_id') and call_details.get('room_name'):
                        print(f"   🔍 Monitoring call status...")
                        status_info = await monitor_call_status(
                            call_details['room_name'],
                            call_details['participant_id'],
                            timeout_seconds=1800
                        )
                        call_details['status_info'] = status_info

                        if status_info['connected']:
                            monitoring_duration = status_info.get('monitoring_duration', 0)
                            call_duration = status_info.get('call_duration', 0)
                            print(f"   ✅ Call connected! Call duration: {call_duration:.1f}s, Monitoring: {monitoring_duration:.1f}s")

                            recording_path = await download_call_recording(
                                phone, call_details['room_name'], batch_info['batch_folder'], timeout_seconds=120
                            )
                            if recording_path:
                                call_details['recording_path'] = recording_path
                                print(f"   🎵 Recording saved with call results")
                        else:
                            monitoring_duration = status_info.get('monitoring_duration', 0)
                            print(f"   ⚠️  Call status: {status_info['status']} - {status_info['reason']} (monitored: {monitoring_duration:.1f}s)")

                        with open(call_file, 'w') as f:
                            json.dump(call_details, f, indent=2)

                    successful_calls.append({
                        'row': row_num,
                        'name': name,
                        'call_details': call_details
                    })

                except Exception as e:
                    failed_calls.append({
                        'row': row_num,
                        'name': name,
                        'phone': phone,
                        'error': str(e)
                    })
                    print(f"❌ [{row_num}] Failed to call {name}: {e}")

                if row_num < max_rows if max_rows else True:
                    print(f"⏳ Waiting {delay_seconds} seconds before next call...")
                    await asyncio.sleep(delay_seconds)

    except FileNotFoundError:
        raise FileNotFoundError(f"CSV file not found: {csv_filename}")
    except Exception as e:
        raise RuntimeError(f"Error processing CSV file: {e}")

    return {
        'csv_file': csv_filename,
        'processed_rows': row_num if 'row_num' in locals() else 0,
        'successful_calls': successful_calls,
        'failed_calls': failed_calls,
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'batch_info': batch_info
    }


async def main():
    parser = argparse.ArgumentParser(
        description="Make LiveKit outbound calls (single or CSV batch)."
    )
    parser.add_argument("--csv", help="CSV file path for batch mode")
    parser.add_argument("--max_rows", type=int, default=None, help="Max rows from CSV to process")
    parser.add_argument("--delay", type=int, default=5, help="Delay between calls in CSV mode")
    parser.add_argument(
        "--agent_id",
        default=os.getenv("CALL_AGENT_ID", "emma"),
        choices=["emma", "jessica", "noah", "richard", "amber"],
        help="Agent identity for dispatch and room metadata"
    )
    # Single-call positional args (ignored if --csv is used)
    parser.add_argument("phone", nargs="?", help="Phone number in international format (+..)")
    parser.add_argument("room_name", nargs="?", help="Optional room name")
    parser.add_argument("participant_name", nargs="?", help="Optional participant display name")

    args = parser.parse_args()

    if args.csv:
        print(f"🚀 Starting batch from {args.csv} with agent_id={args.agent_id}")
        results = await process_csv_calls_async(
            csv_filename=args.csv,
            max_rows=args.max_rows,
            delay_seconds=args.delay,
            monitor_calls=True,
            agent_id=args.agent_id,
        )
        print(f"\n🎉 Batch completed! Successful: {len(results.get('successful_calls', []))}")
        batch_info = results.get('batch_info', {})
        output_csv_filename = "results_summary.csv"
        if batch_info.get('batch_folder'):
            export_results_to_csv_in_batch(results, batch_info['batch_folder'], output_csv_filename)
            print(f"📄 Saved: {batch_info['batch_folder']}/{output_csv_filename}")
        else:
            export_results_to_csv(results, output_csv_filename)
            print(f"📄 Saved: call_results/{output_csv_filename}")
        return

    # Single call mode
    if not args.phone:
        print("❌ Provide a phone number or use --csv. Example:")
        print("   python make_dynamic_call.py +123456789 --agent_id emma")
        print("   python make_dynamic_call.py --csv 'NZ.csv' --agent_id jessica")
        sys.exit(1)

    phone_number = args.phone
    room_name = args.room_name
    participant_name = args.participant_name

    try:
        print(f"🚀 Making outbound call to {phone_number} (agent_id={args.agent_id})...")
        call_details = await make_outbound_call(
            phone_number=phone_number,
            room_name=room_name,
            participant_name=participant_name,
            agent_identity=args.agent_id,
        )

        print("✅ Call initiated successfully!")
        print(f"📞 Phone Number: {call_details['phone_number']}")
        print(f"🏠 Room Name: {call_details['room_name']}")
        print(f"👤 Participant: {call_details['participant_name']}")
        print(f"🆔 Call ID: {call_details.get('call_id', '')}")
        print(f"👥 Participant ID: {call_details.get('participant_id', '')}")
        print(f"🤖 Agent ID: {call_details.get('agent_id', '')}")

        results_path = Path(RESULTS_DIR)
        results_path.mkdir(exist_ok=True)
        safe_call_id = call_details.get('call_id') or datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        output_file = results_path / f"call_details_{safe_call_id}.json"
        with open(output_file, 'w') as f:
            json.dump(call_details, f, indent=2)
        print(f"📄 Call details saved to: {output_file}")

        print("💡 To connect an agent worker, ensure its name matches --agent_id and it's running.")

    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
